"""Use case for downloading from YouTube shortcode."""
import logging

from src.application.dtos.search_dtos import (
    DownloadFromShortcodeRequest,
    DownloadFromShortcodeResponse
)
from src.domain.value_objects.identifiers import ChatId
from src.domain.interfaces.services import (
    IMusicSearchService,
    ITelegramService,
    IFileService
)
from src.infrastructure.services.error_notification_service import get_error_notification_service

logger = logging.getLogger(__name__)


class DownloadFromShortcodeUseCase:
    """Use case for downloading media from YouTube shortcode."""

    def __init__(
        self,
        music_search_service: IMusicSearchService,
        telegram_service: ITelegramService,
        file_service: IFileService
    ):
        self._music_search_service = music_search_service
        self._telegram_service = telegram_service
        self._file_service = file_service

    async def execute(self, request: DownloadFromShortcodeRequest) -> DownloadFromShortcodeResponse:
        """Execute the download from shortcode use case."""
        try:
            logger.info(f"Downloading from shortcode: {request.shortcode}, media_type: {request.media_type}")

            chat_id = ChatId(request.chat_id)

            try:
                bot_username = await self._telegram_service.get_bot_username_public()
            except Exception as e:
                logger.warning(f"Failed to get bot username: {e}, using fallback")
                bot_username = "instasaver_bot"

            download_result = await self._music_search_service.download_from_shortcode(
                shortcode=request.shortcode,
                media_type=request.media_type,
                bot_username=bot_username
            )

            if not download_result.success:
                logger.error(f"Download failed for shortcode {request.shortcode}: {download_result.message}")

                user_message = "Media yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
                await self._telegram_service.send_message(chat_id, user_message)

                try:
                    error_service = get_error_notification_service(request.bot_token)
                    await error_service.notify_admin_error(f"Download failed for shortcode {request.shortcode}: {download_result.message}")
                except Exception as notification_error:
                    logger.error(f"Failed to send admin notification: {notification_error}")

                return DownloadFromShortcodeResponse(
                    success=False,
                    message=user_message
                )

            file_id = None
            try:
                if request.media_type == "audio":
                    file_id = await self._telegram_service.send_audio_by_file_id(
                        chat_id=chat_id,
                        file_id=download_result.telegram_file_id,
                        caption=download_result.title,
                        title=download_result.title,
                        duration=download_result.duration
                    )
                else:  # video
                    file_id = await self._telegram_service.send_video_by_file_id(
                        chat_id=chat_id,
                        file_id=download_result.telegram_file_id,
                        caption=download_result.title  # Use video title as caption
                    )
            except Exception as telegram_error:
                error_str = str(telegram_error).lower()

                if "topic_closed" in error_str or "mavzu yopilgan" in error_str:
                    logger.warning(f"Topic closed error, retrying without thread context: {telegram_error}")
                    try:
                        if request.media_type == "audio":
                            file_id = await self._telegram_service.send_audio_by_file_id(
                                chat_id=chat_id,
                                file_id=download_result.telegram_file_id,
                                caption=download_result.title,
                                title=download_result.title,
                                duration=download_result.duration
                            )
                        else:
                            file_id = await self._telegram_service.send_video_by_file_id(
                                chat_id=chat_id,
                                file_id=download_result.telegram_file_id,
                                caption=download_result.title
                            )
                    except Exception as retry_error:
                        logger.error(f"Failed to send media even after retry: {retry_error}")
                        await self._telegram_service.send_message(
                            chat_id,
                            "Media yuborishda xatolik yuz berdi. Iltimos, boshqa mavzuda yoki shaxsiy xabarda urinib ko'ring."
                        )
                        raise retry_error
                else:
                    raise telegram_error

            return DownloadFromShortcodeResponse(
                success=True,
                message=f"Media yuklandi va yuborildi",
                file_id=file_id
            )

        except Exception as e:
            logger.error(f"Error in download from shortcode use case: {e}")

            # Send user-friendly message
            user_message = "Media yuklab olishda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            try:
                await self._telegram_service.send_message(ChatId(request.chat_id), user_message)
            except Exception as msg_error:
                logger.error(f"Failed to send error message: {msg_error}")

            # Notify admin about error
            try:
                error_service = get_error_notification_service(request.bot_token)
                await error_service.notify_admin_error(f"Download from shortcode failed: {str(e)}")
            except Exception as notification_error:
                logger.error(f"Failed to send admin notification: {notification_error}")

            return DownloadFromShortcodeResponse(
                success=False,
                message=user_message
            )
