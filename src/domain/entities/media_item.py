"""Domain entity for media items."""
from dataclasses import dataclass
from typing import Optional, List
from enum import Enum


class MediaType(Enum):
    """Media type enumeration."""
    IMAGE = "image"
    VIDEO = "video"
    ALBUM = "album"


@dataclass
class MediaItem:
    """Domain entity representing a media item."""
    url: str
    media_type: MediaType
    download_url: Optional[str] = None
    caption: Optional[str] = None
    duration: Optional[int] = None
    width: Optional[int] = None
    height: Optional[int] = None
    thumbnail_url: Optional[str] = None
    file_size: Optional[int] = None
    hosting: Optional[str] = None
    music_url: Optional[str] = None  # For TikTok music/audio


@dataclass
class AlbumItem:
    """Domain entity representing an album item."""
    media_type: MediaType
    download_url: str
    index: int
    total_items: int
    thumbnail_url: Optional[str] = None


@dataclass
class Album:
    """Domain entity representing an album."""
    items: List[AlbumItem]
    caption: Optional[str] = None

    @property
    def total_items(self) -> int:
        return len(self.items)
