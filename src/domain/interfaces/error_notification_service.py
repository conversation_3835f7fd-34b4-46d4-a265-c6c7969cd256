"""Interface for error notification service."""
from abc import ABC, abstractmethod


class IErrorNotificationService(ABC):
    """Interface for error notification service."""

    @abstractmethod
    async def notify_admin_error(self, error_message: str) -> None:
        """
        Notify admin about an error.

        Args:
            error_message: The error message to send to admin
        """
        pass
