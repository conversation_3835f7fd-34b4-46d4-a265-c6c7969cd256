"""Repository interfaces."""
from abc import ABC, abstractmethod


class IMediaRepository(ABC):
    """Interface for media repository."""

    @abstractmethod
    async def save_file(self, file_path: str, content: bytes) -> None:
        """Save file to storage."""
        pass

    @abstractmethod
    async def delete_file(self, file_path: str) -> None:
        """Delete file from storage."""
        pass

    @abstractmethod
    async def file_exists(self, file_path: str) -> bool:
        """Check if file exists."""
        pass

    @abstractmethod
    async def get_file_size(self, file_path: str) -> int:
        """Get file size in bytes."""
        pass



