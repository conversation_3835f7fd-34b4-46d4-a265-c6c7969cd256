"""Text utilities for handling captions and message formatting."""
import logging
import re
from typing import Optional

logger = logging.getLogger(__name__)


class TextUtils:
    """Utility class for text processing and formatting."""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """Clean text by removing problematic characters and normalizing."""
        if not text or not isinstance(text, str):
            return ""
        
        try:
            # Remove control characters except newline, carriage return, and tab
            cleaned = ''.join(char for char in text if ord(char) >= 32 or char in '\n\r\t')
            
            # Normalize whitespace
            cleaned = re.sub(r'\s+', ' ', cleaned.strip())
            
            return cleaned
        except Exception as e:
            logger.warning(f"Error cleaning text: {e}")
            return ""
    
    @staticmethod
    def truncate_text(text: str, max_length: int, suffix: str = "...") -> str:
        """Truncate text to fit within max_length, preserving word boundaries when possible."""
        if not text:
            return ""

        # If text is already within limit, return as is
        if len(text) <= max_length:
            return text

        # If max_length is too small for suffix, just slice without suffix
        if len(suffix) >= max_length:
            return text[:max_length] if max_length > 0 else ""

        # Calculate available space for actual content
        available_length = max_length - len(suffix)

        if available_length <= 0:
            return text[:max_length] if max_length > 0 else ""

        # Try to truncate at word boundary
        truncated = text[:available_length]

        # Find the last space to avoid cutting words in half
        last_space = truncated.rfind(' ')

        # If we found a space and it's not too close to the beginning, use it
        # But only if we don't lose more than 30% of available content
        if last_space > 0 and last_space > available_length * 0.7:
            truncated = truncated[:last_space]

        return truncated + suffix
    
    @staticmethod
    def format_caption_with_bot_mention(
        title: Optional[str],
        bot_username: str,
        max_length: int = 1024
    ) -> str:
        """Format caption with title and bot mention, respecting length limits."""
        bot_mention = f"👉 @{bot_username} dan yuklandi"

        # Clean the title if provided
        if title:
            title = TextUtils.clean_text(title)

        # If no title or empty after cleaning, return just bot mention
        if not title:
            # Even bot mention might be too long, so check and truncate if needed
            if len(bot_mention) > max_length:
                return bot_mention[:max_length-3] + "..."
            return bot_mention

        # Calculate space needed for bot mention (including separators)
        separator = "\n\n"
        bot_mention_with_separator = f"{separator}{bot_mention}"

        # If bot mention with separator alone exceeds limit, return truncated bot mention only
        if len(bot_mention_with_separator) >= max_length:
            if len(bot_mention) > max_length:
                return bot_mention[:max_length-3] + "..."
            return bot_mention

        # Calculate available space for title
        available_for_title = max_length - len(bot_mention_with_separator)

        # Truncate title if necessary
        if len(title) > available_for_title:
            # Reserve 3 characters for "..."
            if available_for_title > 3:
                title = title[:available_for_title-3] + "..."
            else:
                # If very little space, just use bot mention
                if len(bot_mention) > max_length:
                    return bot_mention[:max_length-3] + "..."
                return bot_mention

        # Combine title and bot mention
        result = f"{title}{separator}{bot_mention}"

        # Final safety check - if still too long, force truncate
        if len(result) > max_length:
            logger.warning(f"Caption still too long after truncation: {len(result)} > {max_length}")
            if max_length > 3:
                return result[:max_length-3] + "..."
            else:
                return result[:max_length]

        return result
    
    @staticmethod
    def validate_caption_length(caption: str, max_length: int = 1024) -> bool:
        """Validate if caption length is within Telegram limits."""
        return len(caption) <= max_length
    
    @staticmethod
    def format_duration(seconds: Optional[int]) -> str:
        """Format duration in seconds to human-readable format."""
        if not seconds or seconds <= 0:
            return ""
        
        try:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            secs = seconds % 60
            
            if hours > 0:
                return f"{hours}:{minutes:02d}:{secs:02d}"
            else:
                return f"{minutes}:{secs:02d}"
        except Exception as e:
            logger.warning(f"Error formatting duration {seconds}: {e}")
            return ""
    
    @staticmethod
    def extract_video_id_from_title(title: str) -> Optional[str]:
        """Extract video ID from title if it contains one."""
        if not title:
            return None

        # Look for patterns like "YouTube Video ABC123" or similar
        patterns = [
            r'YouTube Video ([A-Za-z0-9_-]{11})',
            r'Video ID: ([A-Za-z0-9_-]{11})',
            r'\[([A-Za-z0-9_-]{11})\]'
        ]

        for pattern in patterns:
            match = re.search(pattern, title)
            if match:
                return match.group(1)

        return None

    @staticmethod
    def ensure_caption_within_limits(caption: str, max_length: int = 1024) -> str:
        """Ensure caption is within Telegram limits, force truncate if necessary."""
        if not caption:
            return ""

        if len(caption) <= max_length:
            return caption

        # Force truncate with ellipsis
        if max_length > 3:
            return caption[:max_length-3] + "..."
        else:
            return caption[:max_length]

    @staticmethod
    def get_safe_caption_for_media(
        title: Optional[str],
        bot_username: str,
        media_type: str = "media",
        max_length: int = 1024
    ) -> str:
        """Get a safe caption for media that's guaranteed to be within limits."""
        try:
            # First try normal formatting
            caption = TextUtils.format_caption_with_bot_mention(
                title=title,
                bot_username=bot_username,
                max_length=max_length
            )

            # Double-check and force truncate if needed
            return TextUtils.ensure_caption_within_limits(caption, max_length)

        except Exception as e:
            logger.warning(f"Error formatting caption, using fallback: {e}")
            # Fallback to just bot mention
            fallback = f"👉 @{bot_username} dan yuklandi"
            return TextUtils.ensure_caption_within_limits(fallback, max_length)
