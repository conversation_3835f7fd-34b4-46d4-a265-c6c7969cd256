"""Pinterest service implementation using FastSaver API."""
import httpx
import logging
from urllib.parse import quote

from src.domain.interfaces.services import IPinterestService
from src.domain.entities.media_item import MediaItem, MediaType
from src.domain.value_objects.url import Url
from src.infrastructure.config.settings import settings

logger = logging.getLogger(__name__)


class PinterestService(IPinterestService):
    """Pinterest service implementation using FastSaver API."""

    def __init__(self):
        self.api_base_url = settings.pinterest.api_base_url
        self.api_token = settings.pinterest.api_token

    async def get_media_info(self, url: Url) -> MediaItem:
        """Get media information from Pinterest URL."""
        if not self.api_token:
            raise Exception("Pinterest API token not configured. Please set PINTEREST_API_TOKEN environment variable.")

        try:
            encoded_url = quote(url.value, safe='')
            api_url = f"{self.api_base_url}?url={encoded_url}&token={self.api_token}"

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(api_url)
                response.raise_for_status()

                data = response.json()
                if data.get("error"):
                    raise Exception(f"API Error: {data.get('message', 'Unknown error')}")

                # Map API response to domain entity
                media_type_str = data.get("type", "").lower()
                if media_type_str == "video":
                    media_type = MediaType.VIDEO
                else:
                    media_type = MediaType.IMAGE

                return MediaItem(
                    url=url.value,
                    media_type=media_type,
                    download_url=data.get("download_url"),
                    caption=data.get("caption"),
                    thumbnail_url=data.get("thumb"),
                    hosting=data.get("hosting")
                )

        except httpx.RequestError as e:
            logger.error(f"Request error getting Pinterest media info: {e}")
            raise Exception("Failed to connect to Pinterest service")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error getting Pinterest media info: {e.response.status_code} - {e}")
            raise Exception("Pinterest service temporarily unavailable")
        except Exception as e:
            logger.error(f"Error getting Pinterest media info: {e}")
            raise

    async def download_media(self, media_item: MediaItem) -> bytes:
        """Download media content from Pinterest."""
        try:
            download_url = media_item.download_url
            if not download_url:
                raise Exception("No download URL available")

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.get(download_url)
                response.raise_for_status()
                logger.info(f"Downloaded {len(response.content)} bytes from Pinterest")
                return response.content

        except httpx.RequestError as e:
            logger.error(f"Request error downloading Pinterest media: {e}")
            raise Exception(f"Failed to download Pinterest media: {e}")
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error downloading Pinterest media: {e}")
            raise Exception(f"Pinterest media download failed: {e}")
        except Exception as e:
            logger.error(f"Error downloading Pinterest media: {e}")
            raise 