"""Global error handler middleware for FastAPI."""
import logging
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware

from src.presentation.models.response_models import ErrorResponse


logger = logging.getLogger(__name__)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """Middleware to handle all unhandled exceptions with detailed logging."""

    async def dispatch(self, request: Request, call_next):
        """Process request and handle any exceptions with detailed information."""
        try:
            response = await call_next(request)
            return response
        except HTTPException:
            raise

        except Exception as e:
            logger.error(f"Unhandled exception in {request.url.path}: {e}", exc_info=True)

            return JSONResponse(
                status_code=500,
                content=ErrorResponse(
                    status="error",
                    message="Tizimda xatolik. Keyinroq urinib ko'ring."
                ).model_dump()
            )

async def handle_validation_error(request: Request, exc: Exception) -> JSONResponse:
    """Handle validation errors with detailed logging."""
    logger.warning(f"Validation error in {request.url.path}: {exc}")
    return JSONResponse(
        status_code=400,
        content=ErrorResponse(
            status="error",
            message="Noto'g'ri ma'lumot. To'g'ri formatda yuboring.",
            detail=str(exc)
        ).model_dump()
    )


async def handle_http_exception(request: Request, exc: HTTPException) -> JSONResponse:
    """Handle HTTP exceptions with simple logging."""
    logger.warning(f"HTTP exception in {request.url.path}: {exc.status_code} - {exc.detail}")

    user_messages = {
        400: "Noto'g'ri so'rov. Ma'lumotni tekshiring.",
        401: "Ruxsat yo'q. Qayta kirish qiling.",
        403: "Ruxsat yo'q. Bu amalni bajara olmaysiz.",
        404: "Sahifa topilmadi. To'g'ri manzil kiriting.",
        429: "Juda ko'p so'rov. Biroz kuting.",
        500: "Tizimda xatolik. Keyinroq urinib ko'ring.",
        502: "Server javob bermayapti. Keyinroq urinib ko'ring.",
        503: "Servis ishlamayapti. Keyinroq urinib ko'ring."
    }
    user_message = user_messages.get(exc.status_code, "Xatolik yuz berdi.")
    return JSONResponse(
        status_code=exc.status_code,
        content=ErrorResponse(
            status="error",
            message=user_message,
            detail=str(exc.detail) if exc.status_code < 500 else None
        ).model_dump()
    )
