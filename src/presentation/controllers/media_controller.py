"""Media download controller."""
import logging
from datetime import datetime
from fastapi import APIRouter, Request
from fastapi.responses import JSONResponse

from src.presentation.models.request_models import DownloadRequest, DownloadFromShortcodeRequest
from src.presentation.models.response_models import DownloadResponse, ErrorResponse, HealthResponse, SearchMusicResponse, MusicSearchResult
from src.application.dtos.download_dtos import DownloadMediaRequest
from src.application.dtos.search_dtos import SearchMusicRequest as SearchMusicRequestDto, DownloadFromShortcodeRequest as DownloadFromShortcodeRequestDto
from src.presentation.dependencies import (
    get_download_media_use_case,
    get_search_music_use_case,
    get_download_from_shortcode_use_case,
    get_error_notification_service
)


async def extract_bot_token(request: Request) -> str:
    """Extract bot token from request."""
    # Try to get bot token from headers
    bot_token = request.headers.get("X-Bot-Token") or request.headers.get("Authorization")
    if bot_token and bot_token.startswith("Bearer "):
        bot_token = bot_token[7:]  # Remove "Bearer " prefix

    # If not in headers, try to get from query params
    if not bot_token:
        bot_token = request.query_params.get("bot_token")

    return bot_token

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api", tags=["media"])


@router.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint."""
    return HealthResponse(
        status="healthy",
        version="1.0.0",
        timestamp=datetime.now().isoformat()
    )


@router.post("/download", response_model=DownloadResponse)
async def download_media(request: DownloadRequest):
    """Download media from Instagram or YouTube and send to Telegram."""
    try:
        logger.info(f"Received download request for URL: {request.url}")

        use_case = get_download_media_use_case(request.bot_token)

        app_request = DownloadMediaRequest(
            chat_id=request.chat_id,
            url=request.url,
            bot_token=request.bot_token,
            media_type=request.media_type.value,
            video_format=request.video_format
        )

        result = await use_case.execute(app_request)

        if result.success:
            response = DownloadResponse(
                status="success",
                message=result.message,
                file_id=result.file_id
            )

            return response

        response = JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message=result.message
            ).model_dump()
        )

        return response

    except ValueError as exc:
        logger.error(f"Validation error: {exc}")

        try:
            error_service = get_error_notification_service(request.bot_token)
            await error_service.notify_admin_error(f"Validation error in download: {str(exc)}")
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting."
            ).model_dump()
        )

    except Exception as exc:
        logger.error(f"Unexpected error in media controller: {exc}")

        try:
            error_service = get_error_notification_service(request.bot_token)
            await error_service.notify_admin_error(f"Download endpoint error: {str(exc)}")
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")

        user_message = "Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."

        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message=user_message
            ).model_dump()
        )


@router.get("/search-music", response_model=SearchMusicResponse)
async def search_music(query: str, page: int = 1):
    """Search for music using the FastSaver API."""    
    try:
        logger.info(f"Received music search request for query: {query}, page: {page}")

        use_case = get_search_music_use_case()

        app_request = SearchMusicRequestDto(
            query=query,
            page=page
        )

        result = await use_case.execute(app_request)

        search_results = [
            MusicSearchResult(
                title=item.title,
                shortcode=item.shortcode,
                duration=item.duration,
                thumb=item.thumb,
                thumb_best=item.thumb_best
            )
            for item in result.results
        ]

        response = SearchMusicResponse(
            error=result.error,
            page=result.page,
            results=search_results
        )

        return response

    except ValueError as exc:
        logger.error(f"Validation error: {exc}")

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting."
            ).model_dump()
        )

    except Exception as exc:
        logger.error(f"Unexpected error in search music: {exc}")

        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Musiqa qidiruvida xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            ).model_dump()
        )


@router.post("/download-shortcode", response_model=DownloadResponse)
async def download_from_shortcode(request: DownloadFromShortcodeRequest):
    """Download media from YouTube shortcode and send to Telegram."""
    try:
        logger.info(f"Received download from shortcode request: {request.shortcode}")

        use_case = get_download_from_shortcode_use_case(request.bot_token)

        app_request = DownloadFromShortcodeRequestDto(
            chat_id=request.chat_id,
            shortcode=request.shortcode,
            bot_token=request.bot_token,
            media_type=request.media_type.value
        )

        result = await use_case.execute(app_request)

        if result.success:
            response = DownloadResponse(
                status="success",
                message=result.message,
                file_id=result.file_id
            )
            return response
        else:
            response = JSONResponse(
                status_code=400,
                content=ErrorResponse(
                    status="error",
                    message=result.message
                ).model_dump()
            )
            return response

    except ValueError as e:
        logger.error(f"Validation error: {e}")

        try:
            error_service = get_error_notification_service(request.bot_token)
            await error_service.notify_admin_error(f"Validation error in download shortcode: {str(e)}")
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")

        return JSONResponse(
            status_code=400,
            content=ErrorResponse(
                status="error",
                message="Noto'g'ri ma'lumot kiritildi. Iltimos, to'g'ri formatda kiriting."
            ).model_dump()
        )
    except Exception as e:
        logger.error(f"Unexpected error in download from shortcode: {e}")

        try:
            error_service = get_error_notification_service(request.bot_token)
            await error_service.notify_admin_error(f"Download from shortcode error: {str(e)}")
        except Exception as notification_error:
            logger.error(f"Failed to send error notification: {notification_error}")

        return JSONResponse(
            status_code=500,
            content=ErrorResponse(
                status="error",
                message="Serverda xatolik yuz berdi. Iltimos, keyinroq qayta urinib ko'ring."
            ).model_dump()
        )



