#!/usr/bin/env python3
"""Test script to verify FastSaver API get-info endpoint works correctly."""

import asyncio
import httpx
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.infrastructure.services.music_search_service import MusicSearchService
from src.infrastructure.services.youtube_service import YouTubeService


async def test_fastsaver_get_info():
    """Test the FastSaver API get-info endpoint directly."""
    print("Testing FastSaver API get-info endpoint...")
    
    # Test with the provided URL
    test_url = "https://youtu.be/7gMTTcB4Qrg?si=1gGyTbcFSudxcJoa"
    api_url = "https://fastsaverapi.com/get-info"
    token = "lxcMy0OtNaimyGEQkdHjXAmC"
    
    params = {
        "url": test_url,
        "token": token
    }
    
    try:
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.get(api_url, params=params)
            response.raise_for_status()
            
            data = response.json()
            print(f"API Response: {data}")
            
            if data.get("error", False):
                print(f"API Error: {data.get('message', 'Unknown error')}")
                return False
            
            title = data.get("title")
            duration = data.get("duration")
            
            print(f"Title: {title}")
            print(f"Duration: {duration}")
            
            return True
            
    except Exception as e:
        print(f"Error calling FastSaver API: {e}")
        return False


async def test_music_search_service():
    """Test the MusicSearchService with the new get-info functionality."""
    print("\nTesting MusicSearchService...")
    
    service = MusicSearchService()
    
    # Test getting video info
    video_id = "7gMTTcB4Qrg"
    
    try:
        video_info = await service._get_video_info_from_fastsaver(video_id)
        if video_info:
            print(f"Video info from service: {video_info}")
            
            title = await service._get_video_title_from_youtube(video_id)
            print(f"Title from service: {title}")
            
            duration = await service._get_video_duration_from_youtube(video_id)
            print(f"Duration from service: {duration}")
            
            return True
        else:
            print("Failed to get video info from service")
            return False
            
    except Exception as e:
        print(f"Error testing MusicSearchService: {e}")
        return False


async def test_youtube_service():
    """Test the YouTubeService with the new get-info functionality."""
    print("\nTesting YouTubeService...")
    
    service = YouTubeService()
    
    # Test getting video info
    video_id = "7gMTTcB4Qrg"
    
    try:
        video_info = await service._get_video_info_from_fastsaver(video_id)
        if video_info:
            print(f"Video info from YouTube service: {video_info}")
            
            title = await service._get_video_title(video_id)
            print(f"Title from YouTube service: {title}")
            
            duration = await service._get_video_duration(video_id)
            print(f"Duration from YouTube service: {duration}")
            
            return True
        else:
            print("Failed to get video info from YouTube service")
            return False
            
    except Exception as e:
        print(f"Error testing YouTubeService: {e}")
        return False


async def main():
    """Run all tests."""
    print("Starting FastSaver API tests...\n")
    
    # Test direct API call
    api_success = await test_fastsaver_get_info()
    
    # Test MusicSearchService
    music_success = await test_music_search_service()
    
    # Test YouTubeService
    youtube_success = await test_youtube_service()
    
    print(f"\nTest Results:")
    print(f"Direct API call: {'✓' if api_success else '✗'}")
    print(f"MusicSearchService: {'✓' if music_success else '✗'}")
    print(f"YouTubeService: {'✓' if youtube_success else '✗'}")
    
    if all([api_success, music_success, youtube_success]):
        print("\n🎉 All tests passed! FastSaver API integration is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
