"""Tests for domain layer."""
import pytest
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.domain.value_objects.url import Url
from src.domain.value_objects.identifiers import Cha<PERSON><PERSON><PERSON>, BotToken, UserId
from src.domain.entities.media_item import MediaItem, MediaType


class TestUrl:
    """Test URL value object."""

    def test_valid_instagram_url(self):
        """Test valid Instagram URL."""
        url = Url("https://instagram.com/p/ABC123/")
        assert url.is_instagram is True
        assert url.is_youtube is False
        assert url.extract_instagram_shortcode() == "ABC123"

    def test_valid_instagram_url_with_username(self):
        """Test valid Instagram URL with username."""
        url = Url("https://www.instagram.com/progress_reabli_markazi0301/reel/DBrLkgBKKH2/")
        assert url.is_instagram is True
        assert url.is_youtube is False
        assert url.extract_instagram_shortcode() == "DBrLkgBKKH2"

    def test_valid_youtube_url(self):
        """Test valid YouTube URL."""
        url = Url("https://youtube.com/watch?v=dQw4w9WgXcQ")
        assert url.is_youtube is True
        assert url.is_instagram is False
        assert url.extract_youtube_video_id() == "dQw4w9WgXcQ"

    def test_valid_pinterest_short_url(self):
        """Test valid Pinterest short URL (pin.it)."""
        url = Url("https://pin.it/15LwevBbF")
        assert url.is_pinterest is True
        assert url.extract_pinterest_shortcode() == "15LwevBbF"

    def test_valid_pinterest_full_url(self):
        """Test valid Pinterest full URL (pinterest.com/pin/...)."""
        url = Url("https://www.pinterest.com/pin/2885187257334915/")
        assert url.is_pinterest is True
        assert url.extract_pinterest_shortcode() == "2885187257334915"

    def test_invalid_url(self):
        """Test invalid URL."""
        with pytest.raises(ValueError):
            Url("not-a-url")

    def test_empty_url(self):
        """Test empty URL."""
        with pytest.raises(ValueError):
            Url("")

    def test_invalid_pinterest_url(self):
        """Test invalid Pinterest URL."""
        url = Url("https://example.com/anything")
        assert url.is_pinterest is False
        assert url.extract_pinterest_shortcode() is None


class TestIdentifiers:
    """Test identifier value objects."""

    def test_valid_chat_id(self):
        """Test valid chat ID."""
        chat_id = ChatId(123456789)
        assert chat_id.value == 123456789

    def test_invalid_chat_id(self):
        """Test invalid chat ID."""
        with pytest.raises(ValueError):
            ChatId("not-an-int")

    def test_valid_bot_token(self):
        """Test valid bot token."""
        token = BotToken("1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk")
        assert len(token.value) > 10

    def test_invalid_bot_token(self):
        """Test invalid bot token."""
        with pytest.raises(ValueError):
            BotToken("short")

    def test_empty_bot_token(self):
        """Test empty bot token."""
        with pytest.raises(ValueError):
            BotToken("")


class TestMediaItem:
    """Test MediaItem entity."""

    def test_create_media_item(self):
        """Test creating a media item."""
        media = MediaItem(
            url="https://instagram.com/p/ABC123/",
            media_type=MediaType.IMAGE,
            download_url="https://example.com/image.jpg",
            caption="Test caption",
        )
        
        assert media.url == "https://instagram.com/p/ABC123/"
        assert media.media_type == MediaType.IMAGE
        assert media.download_url == "https://example.com/image.jpg"
        assert media.caption == "Test caption"

    def test_media_types(self):
        """Test media type enumeration."""
        assert MediaType.IMAGE.value == "image"
        assert MediaType.VIDEO.value == "video"
        assert MediaType.ALBUM.value == "album"
