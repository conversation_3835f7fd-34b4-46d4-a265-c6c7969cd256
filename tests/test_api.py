"""Tests for API endpoints."""
import pytest
import sys
import os
from fastapi.testclient import TestClient

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app import app

client = TestClient(app)


class TestHealthEndpoint:
    """Test health check endpoint."""

    def test_health_check(self):
        """Test health check endpoint."""
        response = client.get("/api/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert data["version"] == "1.0.0"
        assert "timestamp" in data


class TestDownloadEndpoint:
    """Test download endpoint."""

    def test_download_invalid_request(self):
        """Test download with invalid request."""
        response = client.post("/api/download", json={
            "chat_id": "not-an-int",
            "url": "invalid-url",
            "bot_token": "short"
        })
        assert response.status_code in [400, 422]  # Validation error

    def test_download_missing_fields(self):
        """Test download with missing fields."""
        response = client.post("/api/download", json={
            "chat_id": 123456789
            # Missing url and bot_token
        })
        assert response.status_code in [400, 422]  # Validation error

    def test_download_valid_request_structure(self):
        """Test download with valid request structure (will fail due to invalid bot token)."""
        response = client.post("/api/download", json={
            "chat_id": 123456789,
            "url": "https://instagram.com/p/ABC123/",
            "bot_token": "1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        })
        # This will fail due to invalid bot token, but the structure should be valid
        assert response.status_code in [400, 500]  # Business logic error, not validation error

    def test_download_pinterest(self):
        """Test download with valid Pinterest URL (structure only, will fail on bot token)."""
        response = client.post("/api/download", json={
            "chat_id": 123456789,
            "url": "https://pin.it/15LwevBbF",
            "bot_token": "1234567890:ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijk"
        })
        # This will fail due to invalid bot token, but the structure should be valid
        assert response.status_code in [400, 500]  # Business logic error, not validation error
